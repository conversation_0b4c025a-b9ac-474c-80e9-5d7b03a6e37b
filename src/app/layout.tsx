import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: '<PERSON> - Software Engineer',
  description: 'Software engineer with over 4 years experience in Engineering, Distributed Systems, Data Ingestion, and Cloud infrastructure.',
  keywords: ['Software Engineer', 'Python', 'Go', 'Cloud Infrastructure', 'GCP', 'AWS'],
  authors: [{ name: '<PERSON>' }],
  openGraph: {
    title: '<PERSON> - Software Engineer',
    description: 'Software engineer with over 4 years experience in Engineering, Distributed Systems, Data Ingestion, and Cloud infrastructure.',
    url: 'https://coryjanowski.vercel.app',
    siteName: '<PERSON>',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: '<PERSON> - Software Engineer',
    description: 'Software engineer with over 4 years experience in Engineering, Distributed Systems, Data Ingestion, and Cloud infrastructure.',
  },
  robots: {
    index: true,
    follow: true,
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className="bg-apple-gray-50 text-apple-gray-700 antialiased">
        {children}
      </body>
    </html>
  )
}
