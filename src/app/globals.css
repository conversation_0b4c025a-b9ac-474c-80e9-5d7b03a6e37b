@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

@layer components {
  .section-padding {
    @apply px-6 sm:px-8 lg:px-12 xl:px-16;
  }

  .container-max {
    @apply max-w-6xl mx-auto;
  }

  /* Glass Tile Effects */
  .glass-tile {
    @apply backdrop-blur-md;
    background: linear-gradient(145deg, 
      rgba(255, 255, 255, 0.1) 0%, 
      rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  .glass-tile:hover {
    background: linear-gradient(145deg, 
      rgba(255, 255, 255, 0.15) 0%, 
      rgba(255, 255, 255, 0.08) 100%);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 
      0 16px 48px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }

  .glass-tile-subtle {
    @apply backdrop-blur-sm;
    background: linear-gradient(145deg, 
      rgba(255, 255, 255, 0.08) 0%, 
      rgba(255, 255, 255, 0.03) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 
      0 4px 16px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .glass-tile-subtle:hover {
    background: linear-gradient(145deg, 
      rgba(255, 255, 255, 0.12) 0%, 
      rgba(255, 255, 255, 0.06) 100%);
    border: 1px solid rgba(255, 255, 255, 0.25);
    box-shadow: 
      0 8px 24px rgba(0, 0, 0, 0.12),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  /* Glass Section Backgrounds */
  .glass-section-light {
    position: relative;
    background: linear-gradient(135deg, 
      rgba(248, 250, 252, 0.8) 0%, 
      rgba(241, 245, 249, 0.9) 100%);
  }

  .glass-section-light::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
      rgba(255, 255, 255, 0.1) 0%, 
      transparent 50%, 
      rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
  }

  .glass-section-white {
    position: relative;
    background: linear-gradient(135deg, 
      rgba(255, 255, 255, 0.95) 0%, 
      rgba(249, 250, 251, 0.98) 100%);
  }

  .glass-section-white::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
      rgba(255, 255, 255, 0.2) 0%, 
      transparent 50%, 
      rgba(255, 255, 255, 0.1) 100%);
    pointer-events: none;
  }

  /* Animated Gradient Text Effects */
  .gradient-title {
    background: linear-gradient(135deg, #6a11cb 0%, #fc5c7d 50%, #fdc830 100%);
    background-size: 200% 200%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-animation 8s ease infinite;
  }

  .gradient-title-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    background-size: 300% 300%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-animation-slow 12s ease infinite;
  }

  .gradient-title-projects {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 25%, #ff9ff3 50%, #54a0ff 75%, #5f27cd 100%);
    background-size: 250% 250%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-animation-fast 6s ease infinite;
  }

  .gradient-title-tech {
    background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 25%, #00d2ff 50%, #928dab 75%, #1e3c72 100%);
    background-size: 200% 200%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-animation 10s ease infinite;
  }

  .gradient-title-experience {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 25%, #fa709a 50%, #fee140 75%, #fa709a 100%);
    background-size: 200% 200%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-animation-slow 14s ease infinite;
  }

  .gradient-title-education {
    background: linear-gradient(135deg, #d299c2 0%, #fef9d7 25%, #dab785 50%, #c9a96e 75%, #8b4a7e 100%);
    background-size: 200% 200%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-animation 9s ease infinite;
  }

  .gradient-title-thor {
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 25%, #ff9ff3 50%, #54a0ff 75%, #5f27cd 100%);
    background-size: 200% 200%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-animation-fast 7s ease infinite;
  }

  .gradient-title-family {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 25%, #fecfef 50%, #ffecd2 75%, #fcb69f 100%);
    background-size: 200% 200%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-animation-slow 11s ease infinite;
  }

  .gradient-logo {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    background-size: 300% 300%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-animation-slow 15s ease infinite;
  }

  .gradient-name {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    background-size: 300% 300%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-animation-slow 12s ease infinite;
  }

  .gradient-title-text {
    background: linear-gradient(135deg, #6a11cb 0%, #fc5c7d 50%, #fdc830 100%);
    background-size: 200% 200%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-animation 8s ease infinite;
  }

  /* Enhanced Button Effects */
  .btn-call-enhanced {
    background: linear-gradient(135deg, #10b981 0%, #059669 25%, #0d9488 50%, #0891b2 75%, #0e7490 100%);
    background-size: 200% 200%;
    animation: gradient-animation-fast 8s ease infinite;
    position: relative;
    overflow: hidden;
  }

  .btn-call-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
  }

  .btn-call-enhanced:hover::before {
    left: 100%;
  }

  .btn-pulse {
    animation: gentle-pulse 3s ease-in-out infinite;
  }

  .btn-email-enhanced {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    background-size: 200% 200%;
    animation: gradient-animation-slow 10s ease infinite;
    position: relative;
    overflow: hidden;
  }

  .btn-email-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
  }

  .btn-email-enhanced:hover::before {
    left: 100%;
  }

  .btn-linkedin-enhanced {
    background: linear-gradient(135deg, #0077b5 0%, #0e76a8 25%, #00a0dc 50%, #2867b2 75%, #004182 100%);
    background-size: 200% 200%;
    animation: gradient-animation 7s ease infinite;
    position: relative;
    overflow: hidden;
  }

  .btn-linkedin-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
  }

  .btn-linkedin-enhanced:hover::before {
    left: 100%;
  }

  .btn-github-enhanced {
    background: linear-gradient(135deg, #24292e 0%, #586069 25%, #6f42c1 50%, #2188ff 75%, #0366d6 100%);
    background-size: 200% 200%;
    animation: gradient-animation-fast 6s ease infinite;
    position: relative;
    overflow: hidden;
  }

  .btn-github-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
  }

  .btn-github-enhanced:hover::before {
    left: 100%;
  }

  .btn-pulse-email {
    animation: gentle-pulse-email 3.5s ease-in-out infinite;
  }

  .btn-pulse-linkedin {
    animation: gentle-pulse-linkedin 4s ease-in-out infinite;
  }

  .btn-pulse-github {
    animation: gentle-pulse-github 3.2s ease-in-out infinite;
  }
}

/* Gradient Animation Keyframes */
@keyframes gradient-animation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes gradient-animation-slow {
  0% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 100% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes gradient-animation-fast {
  0% {
    background-position: 0% 0%;
  }
  25% {
    background-position: 100% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}

@keyframes gentle-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(16, 185, 129, 0);
  }
}

@keyframes gentle-pulse-email {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(102, 126, 234, 0);
  }
}

@keyframes gentle-pulse-linkedin {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(0, 119, 181, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(0, 119, 181, 0);
  }
}

@keyframes gentle-pulse-github {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(36, 41, 46, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(36, 41, 46, 0);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}